# Production Environment Configuration

# OpenAI Configuration
OPENAI_API_KEY=your_production_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=False

# Vector Database Configuration
VECTOR_DB_TYPE=faiss
VECTOR_DB_PATH=/app/vector_store

# CORS Configuration (Update with your production domains)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=/app/uploads

# Logging Configuration
LOG_LEVEL=INFO

# Optional: Database Configuration (if using MongoDB for chat history)
# MONGODB_URL=mongodb://your-mongodb-host:27017
# DATABASE_NAME=chatbot_production

# Optional: Authentication (if implementing user login)
# SECRET_KEY=your_very_secure_secret_key_here
# ALGORITHM=HS256
# ACCESS_TOKEN_EXPIRE_MINUTES=30

# Optional: Redis Configuration (for session storage)
# REDIS_URL=redis://redis:6379

# Optional: Monitoring and Analytics
# SENTRY_DSN=your_sentry_dsn_here
# ANALYTICS_ENABLED=true
