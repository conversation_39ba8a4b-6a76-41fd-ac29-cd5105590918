2025-07-02 23:47:59,061 - chatbot - INFO - memory.py:43 - Initialized buffer_window memory with k=10, max_tokens=2000
2025-07-02 23:47:59,062 - chatbot - INFO - memory.py:215 - Created new memory for session: 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:47:59,062 - chatbot - INFO - chat.py:90 - Processing chat for session 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:47:59,062 - chatbot - INFO - chat.py:99 - Using mock response for session 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:48:05,514 - chatbot - INFO - chat.py:90 - Processing chat for session 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:48:05,516 - chatbot - INFO - chat.py:99 - Using mock response for session 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:50:11,411 - chatbot - INFO - vectorstore.py:101 - Initialized FAISS vector store with 0 documents
2025-07-02 23:50:11,411 - chatbot - INFO - vectorstore.py:279 - Initialized vector store manager with faiss
2025-07-02 23:50:11,412 - chatbot - INFO - document_processor.py:229 - Processed text content into 1 chunks
2025-07-02 23:50:11,412 - chatbot - INFO - vectorstore.py:134 - Added 1 documents to vector store
2025-07-02 23:50:11,413 - chatbot - INFO - vectorstore.py:215 - Saved vector store to vector_store
2025-07-02 23:51:04,468 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:51:24,080 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:51:34,575 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:51:46,456 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:52:04,471 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:52:19,652 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:53:24,008 - chatbot - INFO - upload.py:65 - Uploaded file test_document.txt as b2dc0787-2667-44c0-8a66-11424c7ce570.txt
2025-07-02 23:53:24,009 - chatbot - ERROR - upload.py:107 - Upload error for test_document.txt: FAISS is not installed. Install with: pip install faiss-cpu
2025-07-02 23:54:02,332 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:54:21,912 - chatbot - INFO - upload.py:65 - Uploaded file test_document.txt as cabc502a-165b-4e36-a24e-2cd56250ecab.txt
2025-07-02 23:54:21,913 - chatbot - ERROR - upload.py:107 - Upload error for test_document.txt: FAISS is not installed. Install with: pip install faiss-cpu
2025-07-02 23:54:29,358 - chatbot - INFO - vectorstore.py:246 - Loaded vector store with 1 documents
2025-07-02 23:54:29,358 - chatbot - INFO - vectorstore.py:101 - Initialized FAISS vector store with 1 documents
2025-07-02 23:54:29,358 - chatbot - INFO - vectorstore.py:279 - Initialized vector store manager with faiss
2025-07-02 23:55:48,491 - chatbot - INFO - upload.py:65 - Uploaded file test_document.txt as b28fedb5-4734-4687-9cc1-036eb323a66f.txt
2025-07-02 23:55:48,494 - chatbot - INFO - vectorstore.py:246 - Loaded vector store with 1 documents
2025-07-02 23:55:48,495 - chatbot - INFO - vectorstore.py:101 - Initialized FAISS vector store with 1 documents
2025-07-02 23:55:48,495 - chatbot - INFO - vectorstore.py:279 - Initialized vector store manager with faiss
2025-07-02 23:55:48,495 - chatbot - INFO - qa_rag.py:31 - Initialized RAG system
2025-07-02 23:55:48,496 - chatbot - INFO - document_processor.py:132 - Processed text file uploads/b28fedb5-4734-4687-9cc1-036eb323a66f.txt into 2 chunks
2025-07-02 23:55:48,498 - chatbot - INFO - vectorstore.py:134 - Added 2 documents to vector store
2025-07-02 23:55:48,499 - chatbot - INFO - vectorstore.py:215 - Saved vector store to vector_store
2025-07-02 23:55:48,499 - chatbot - INFO - qa_rag.py:81 - Added document uploads/b28fedb5-4734-4687-9cc1-036eb323a66f.txt with 2 chunks to RAG system
2025-07-02 23:55:48,499 - chatbot - INFO - upload.py:87 - Successfully processed test_document.txt for RAG with 2 chunks
2025-07-02 23:55:57,051 - chatbot - INFO - memory.py:43 - Initialized buffer_window memory with k=10, max_tokens=2000
2025-07-02 23:55:57,051 - chatbot - INFO - memory.py:215 - Created new memory for session: 7301a97f-1bf7-4950-b89d-b16424bc641f
2025-07-02 23:55:57,051 - chatbot - INFO - chat.py:91 - Processing chat for session 7301a97f-1bf7-4950-b89d-b16424bc641f
2025-07-02 23:55:57,078 - chatbot - INFO - qa_rag.py:179 - No documents found above similarity threshold 0.7
2025-07-02 23:55:57,079 - chatbot - INFO - chat.py:109 - Using mock response for session 7301a97f-1bf7-4950-b89d-b16424bc641f (RAG: True)
2025-07-02 23:56:28,798 - chatbot - INFO - qa_rag.py:179 - No documents found above similarity threshold 0.7
2025-07-02 23:56:56,439 - chatbot - INFO - vectorstore.py:246 - Loaded vector store with 3 documents
2025-07-02 23:56:56,439 - chatbot - INFO - vectorstore.py:101 - Initialized FAISS vector store with 3 documents
2025-07-02 23:56:56,439 - chatbot - INFO - vectorstore.py:279 - Initialized vector store manager with faiss
2025-07-02 23:56:56,439 - chatbot - INFO - qa_rag.py:31 - Initialized RAG system
2025-07-02 23:56:56,462 - chatbot - INFO - qa_rag.py:179 - No documents found above similarity threshold 100.0
2025-07-02 23:57:51,514 - chatbot - INFO - vectorstore.py:246 - Loaded vector store with 3 documents
2025-07-02 23:57:51,514 - chatbot - INFO - vectorstore.py:101 - Initialized FAISS vector store with 3 documents
2025-07-02 23:57:51,514 - chatbot - INFO - vectorstore.py:279 - Initialized vector store manager with faiss
2025-07-02 23:57:51,514 - chatbot - INFO - qa_rag.py:31 - Initialized RAG system
2025-07-02 23:57:51,525 - chatbot - INFO - qa_rag.py:174 - Found 3 documents with scores: [176.53677368164062, 186.30947875976562, 377.4006042480469]
2025-07-02 23:57:51,525 - chatbot - INFO - qa_rag.py:208 - Retrieved 3 relevant documents for query
2025-07-02 23:57:59,379 - chatbot - INFO - memory.py:43 - Initialized buffer_window memory with k=10, max_tokens=2000
2025-07-02 23:57:59,380 - chatbot - INFO - memory.py:215 - Created new memory for session: 0d739485-d6f8-4ed7-afbc-a8617b1081a8
2025-07-02 23:57:59,380 - chatbot - INFO - chat.py:91 - Processing chat for session 0d739485-d6f8-4ed7-afbc-a8617b1081a8
2025-07-02 23:57:59,380 - chatbot - INFO - qa_rag.py:174 - Found 3 documents with scores: [107.96998596191406, 118.6334228515625, 384.5024719238281]
2025-07-02 23:57:59,380 - chatbot - INFO - qa_rag.py:208 - Retrieved 3 relevant documents for query
2025-07-02 23:57:59,380 - chatbot - INFO - chat.py:109 - Using mock response for session 0d739485-d6f8-4ed7-afbc-a8617b1081a8 (RAG: True)
2025-07-03 00:01:07,358 - chatbot - INFO - memory.py:43 - Initialized buffer_window memory with k=10, max_tokens=2000
2025-07-03 00:01:07,361 - chatbot - INFO - memory.py:215 - Created new memory for session: f6e41e8f-e4ff-4c53-9a5a-c14ce33d5321
2025-07-03 00:01:07,361 - chatbot - INFO - chat.py:91 - Processing chat for session f6e41e8f-e4ff-4c53-9a5a-c14ce33d5321
2025-07-03 00:01:07,362 - chatbot - INFO - chat.py:109 - Using mock response for session f6e41e8f-e4ff-4c53-9a5a-c14ce33d5321 (RAG: False)
2025-07-03 00:01:39,255 - chatbot - INFO - memory.py:43 - Initialized buffer_window memory with k=10, max_tokens=2000
2025-07-03 00:01:39,256 - chatbot - INFO - memory.py:215 - Created new memory for session: f9a34475-c179-4899-b7c2-9f4164974275
2025-07-03 00:01:39,256 - chatbot - INFO - chat.py:91 - Processing chat for session f9a34475-c179-4899-b7c2-9f4164974275
2025-07-03 00:01:39,256 - chatbot - INFO - chat.py:109 - Using mock response for session f9a34475-c179-4899-b7c2-9f4164974275 (RAG: False)
