2025-07-02 23:47:59,061 - chatbot - INFO - memory.py:43 - Initialized buffer_window memory with k=10, max_tokens=2000
2025-07-02 23:47:59,062 - chatbot - INFO - memory.py:215 - Created new memory for session: 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:47:59,062 - chatbot - INFO - chat.py:90 - Processing chat for session 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:47:59,062 - chatbot - INFO - chat.py:99 - Using mock response for session 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:48:05,514 - chatbot - INFO - chat.py:90 - Processing chat for session 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:48:05,516 - chatbot - INFO - chat.py:99 - Using mock response for session 11827220-6465-41f7-b9fe-48de8a103dda
2025-07-02 23:50:11,411 - chatbot - INFO - vectorstore.py:101 - Initialized FAISS vector store with 0 documents
2025-07-02 23:50:11,411 - chatbot - INFO - vectorstore.py:279 - Initialized vector store manager with faiss
2025-07-02 23:50:11,412 - chatbot - INFO - document_processor.py:229 - Processed text content into 1 chunks
2025-07-02 23:50:11,412 - chatbot - INFO - vectorstore.py:134 - Added 1 documents to vector store
2025-07-02 23:50:11,413 - chatbot - INFO - vectorstore.py:215 - Saved vector store to vector_store
2025-07-02 23:51:04,468 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:51:24,080 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:51:34,575 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:51:46,456 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:52:04,471 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:52:19,652 - chatbot - WARNING - document_processor.py:103 - No PDF processing libraries available. PDF support disabled.
2025-07-02 23:53:24,008 - chatbot - INFO - upload.py:65 - Uploaded file test_document.txt as b2dc0787-2667-44c0-8a66-11424c7ce570.txt
2025-07-02 23:53:24,009 - chatbot - ERROR - upload.py:107 - Upload error for test_document.txt: FAISS is not installed. Install with: pip install faiss-cpu
