# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=True

# Vector Database Configuration
VECTOR_DB_TYPE=faiss  # Options: faiss, chromadb
VECTOR_DB_PATH=./vector_store

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# Optional: Database Configuration (if using MongoDB for chat history)
# MONGODB_URL=mongodb://localhost:27017
# DATABASE_NAME=chatbot_db

# Optional: Authentication (if implementing user login)
# SECRET_KEY=your_secret_key_here
# ALGORITHM=HS256
# ACCESS_TOKEN_EXPIRE_MINUTES=30
