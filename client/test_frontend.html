<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Test - Conversational AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>🧪 Frontend Integration Tests</h1>
    <p>This page tests the frontend integration with our Conversational AI backend.</p>

    <div class="test-section">
        <h2>API Connectivity Tests</h2>
        <button onclick="testHealthCheck()">Test Health Check</button>
        <button onclick="testChatModes()">Test Chat Modes</button>
        <button onclick="testBasicChat()">Test Basic Chat</button>
        <button onclick="testRAGSearch()">Test RAG Search</button>
        <button onclick="runAllTests()">Run All Tests</button>
    </div>

    <div id="results"></div>

    <script>
        const API_BASE = '/api';
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testHealthCheck() {
            try {
                addResult('Testing health check...', 'info');
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok && data.status === 'healthy') {
                    addResult('✅ Health check passed', 'success');
                    addResult(`Gemini configured: ${data.gemini_configured}, Vector DB: ${data.vector_db_type}`, 'info');
                } else {
                    addResult('❌ Health check failed', 'error');
                }
            } catch (error) {
                addResult(`❌ Health check error: ${error.message}`, 'error');
            }
        }

        async function testChatModes() {
            try {
                addResult('Testing chat modes...', 'info');
                const response = await fetch(`${API_BASE}/chat/modes`);
                const data = await response.json();
                
                if (response.ok && data.modes && data.modes.length >= 3) {
                    addResult('✅ Chat modes loaded successfully', 'success');
                    addResult(`Available modes: ${data.modes.map(m => m.name).join(', ')}`, 'info');
                } else {
                    addResult('❌ Chat modes test failed', 'error');
                }
            } catch (error) {
                addResult(`❌ Chat modes error: ${error.message}`, 'error');
            }
        }

        async function testBasicChat() {
            try {
                addResult('Testing basic chat...', 'info');
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: 'Hello, this is a test message',
                        mode: 'assistant'
                    })
                });
                const data = await response.json();
                
                if (response.ok && data.response && data.session_id) {
                    addResult('✅ Basic chat test passed', 'success');
                    addResult(`Session ID: ${data.session_id.substring(0, 8)}...`, 'info');
                    addResult(`Response: ${data.response.substring(0, 100)}...`, 'info');
                } else {
                    addResult('❌ Basic chat test failed', 'error');
                }
            } catch (error) {
                addResult(`❌ Basic chat error: ${error.message}`, 'error');
            }
        }

        async function testRAGSearch() {
            try {
                addResult('Testing RAG search...', 'info');
                const response = await fetch(`${API_BASE}/chat/rag/search?query=refund%20policy&k=3`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ RAG search test passed', 'success');
                    addResult(`Found ${data.total_found} documents`, 'info');
                    if (data.context) {
                        addResult(`Context preview: ${data.context.substring(0, 100)}...`, 'info');
                    }
                } else {
                    addResult('❌ RAG search test failed', 'error');
                }
            } catch (error) {
                addResult(`❌ RAG search error: ${error.message}`, 'error');
            }
        }

        async function testRAGStats() {
            try {
                addResult('Testing RAG stats...', 'info');
                const response = await fetch(`${API_BASE}/chat/rag/stats`);
                const data = await response.json();
                
                if (response.ok && data.vector_store_stats) {
                    addResult('✅ RAG stats test passed', 'success');
                    addResult(`Vector store has ${data.vector_store_stats.total_documents} documents`, 'info');
                } else {
                    addResult('❌ RAG stats test failed', 'error');
                }
            } catch (error) {
                addResult(`❌ RAG stats error: ${error.message}`, 'error');
            }
        }

        async function testSessionContinuity() {
            try {
                addResult('Testing session continuity...', 'info');
                
                // First message
                const response1 = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: 'My name is Alice',
                        mode: 'assistant'
                    })
                });
                const data1 = await response1.json();
                
                if (!response1.ok) {
                    addResult('❌ Session continuity test failed (first message)', 'error');
                    return;
                }
                
                // Second message with same session
                const response2 = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: 'What is my name?',
                        session_id: data1.session_id,
                        mode: 'assistant'
                    })
                });
                const data2 = await response2.json();
                
                if (response2.ok && data2.session_id === data1.session_id) {
                    addResult('✅ Session continuity test passed', 'success');
                    addResult(`Messages in session: ${data2.memory_stats.total_messages}`, 'info');
                } else {
                    addResult('❌ Session continuity test failed', 'error');
                }
            } catch (error) {
                addResult(`❌ Session continuity error: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            addResult('🚀 Running all frontend tests...', 'info');
            resultsDiv.innerHTML = '';
            
            await testHealthCheck();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testChatModes();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testBasicChat();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRAGSearch();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRAGStats();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSessionContinuity();
            
            addResult('🎉 All frontend tests completed!', 'success');
        }

        // Auto-run basic connectivity test on page load
        window.addEventListener('load', () => {
            addResult('Frontend test page loaded', 'info');
            addResult('Click buttons above to run individual tests or "Run All Tests" for complete suite', 'info');
        });
    </script>
</body>
</html>
